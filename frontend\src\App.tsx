import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useTheme } from './contexts/ThemeContext';
import { UserProvider } from './contexts/UserContext';
// import { useState, useEffect } from 'react'; // Removed useState, useEffect
import UploadScanPage from './pages/UploadScanPage';
import KvKUploadScanPage from './pages/KvKUploadScanPage';
import ScansListPage from './pages/ScansListPage';
import ScanDetailPage from './pages/ScanDetailPage';
import DashboardPage from './pages/DashboardPage';
import PerformanceMetricsPage from './pages/PerformanceMetricsPage';
import PlayerAnalyticsPage from './pages/PlayerAnalyticsPage';
import AllianceAnalyticsPage from './pages/AllianceAnalyticsPage';
import KvKDetailPage from './pages/KvKDetailPage';
import LoginPage from './pages/LoginPage';
import UnauthorizedPage from './pages/UnauthorizedPage';
import KvKHistoryPage from './pages/KvKHistoryPage';
import CreateKvKPage from './pages/CreateKvKPage';
import Navbar from './components/Navbar';
import ProtectedRoute from './components/ProtectedRoute';
// import DataLoadingError from './components/DataLoadingError'; // Removed DataLoadingError
// import { checkMockDataLoaded } from './services/dataLoader'; // Removed checkMockDataLoaded

const queryClient = new QueryClient();

function App() {
  const { theme } = useTheme();
  // const [dataLoaded, setDataLoaded] = useState<boolean>(true); // Removed dataLoaded state

  // useEffect(() => { // Removed useEffect
  //   // Check if mock data is loaded correctly
  //   const isDataLoaded = checkMockDataLoaded();
  //   setDataLoaded(isDataLoaded);

  //   if (!isDataLoaded) {
  //     console.error('Mock data failed to load. Application may not function correctly.');
  //   }
  // }, []);

  // if (!dataLoaded) { // Removed conditional rendering for dataLoaded
  //   return (
  //     <div className={`min-h-screen ${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900 text-white'}`}>
  //       <DataLoadingError
  //         message="Failed to load application data"
  //         details="The mock data required for this application could not be loaded. Please check the console for more information or try refreshing the page."
  //       />
  //     </div>
  //   );
  // }

  return (
    <QueryClientProvider client={queryClient}>
      <UserProvider>
        <Router>
          <div className={`min-h-screen ${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900 text-white'}`}>
            <Navbar />
            <main className="container mx-auto px-4 pt-20 pb-8">
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<LoginPage />} />
                <Route path="/unauthorized" element={<UnauthorizedPage />} />

                {/* Public routes - accessible to everyone */}
                <Route path="/" element={<DashboardPage />} />
                <Route path="/performance" element={<PerformanceMetricsPage />} />
                <Route path="/player-analytics" element={<PlayerAnalyticsPage />} />
                <Route path="/alliance-analytics" element={<AllianceAnalyticsPage />} />
                <Route path="/kvk/:kvkId" element={<KvKDetailPage />} />
                <Route path="/kvk-history" element={<KvKHistoryPage />} />

                {/* Admin-only routes */}
                <Route path="/upload-scan" element={
                  <ProtectedRoute requiredPermission="upload_scans">
                    <UploadScanPage />
                  </ProtectedRoute>
                } />
                <Route path="/upload" element={
                  <ProtectedRoute requiredPermission="upload_scans">
                    <KvKUploadScanPage />
                  </ProtectedRoute>
                } />
                <Route path="/kvk/:kvkId/upload" element={
                  <ProtectedRoute requiredPermission="upload_scans">
                    <KvKUploadScanPage />
                  </ProtectedRoute>
                } />
                <Route path="/create-kvk" element={
                  <ProtectedRoute requiredPermission="manage_kvk">
                    <CreateKvKPage />
                  </ProtectedRoute>
                } />
                <Route path="/scans" element={
                  <ProtectedRoute requiredPermission="view_admin_pages">
                    <ScansListPage />
                  </ProtectedRoute>
                } />
                <Route path="/scans/:scanId" element={
                  <ProtectedRoute requiredPermission="view_admin_pages">
                    <ScanDetailPage />
                  </ProtectedRoute>
                } />
              </Routes>
            </main>
          </div>
        </Router>
      </UserProvider>
    </QueryClientProvider>
  );
}

export default App;