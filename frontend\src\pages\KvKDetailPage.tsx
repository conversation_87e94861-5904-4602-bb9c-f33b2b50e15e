import React, { useState, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useQuery } from '@tanstack/react-query';
import { getKvKById, getScansByKvK, getKvKPerformanceSummary } from '../api/api';
import { formatLargeNumber, formatDate, timeAgo } from '../utils/formatters';
import PlayerTable from '../components/PlayerTable';
import KvKPlayerTable from '../components/KvKPlayerTable';
import KvKPerformanceDashboard from '../components/KvKPerformanceDashboard';
import { PlayerScanData } from '../types/dataTypes';
import {
  FaChartLine,
  FaCrosshairs,
  FaShieldAlt,
  FaSkullCrossbones,
  FaInfoCircle,
  FaCalendarAlt,
  FaUsers,
  FaListOl,
  FaExclamationCircle,
  FaUpload,
  FaTrophy,
  FaFire,
  FaCrown,
  FaArrowLeft,
  FaEye
} from 'react-icons/fa';

// Define tabs for the KvK detail page
type TabType = 'overview' | 'killpoints' | 'deads' | 't45kills';

const KvKDetailPage: React.FC = () => {
  const { kvkId } = useParams<{ kvkId: string }>();
  const { theme } = useTheme();
  const { canUploadScans } = useUser();
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  // Fetch KvK data
  const { data: kvk, isLoading: isLoadingKvK } = useQuery({
    queryKey: ['kvk', kvkId],
    queryFn: () => getKvKById(kvkId || ''),
    enabled: !!kvkId
  });

  // Fetch scans for this KvK
  const { data: kvkScans = [], isLoading: isLoadingScans } = useQuery({
    queryKey: ['kvkScans', kvkId],
    queryFn: () => getScansByKvK(kvkId || ''),
    enabled: !!kvkId
  });

  // Fetch KvK performance summary with real data (no limit to get all players)
  const { data: performanceData, isLoading: isLoadingPerformance } = useQuery({
    queryKey: ['kvkPerformance', kvkId],
    queryFn: () => getKvKPerformanceSummary(kvkId || '', 10000), // High limit to get all players
    enabled: !!kvkId
  });

  // kvkScans is already filtered by the API call, no need to filter again

  // Sort scans by date (newest first)
  const sortedScans = useMemo(() => {
    if (!kvkScans || kvkScans.length === 0) return [];
    return [...kvkScans].sort((a, b) => {
      const dateA = a.date ? new Date(a.date).getTime() : 0;
      const dateB = b.date ? new Date(b.date).getTime() : 0;
      return dateB - dateA;
    });
  }, [kvkScans]);

  // Get latest scan
  const latestScan = sortedScans[0];

  // Get baseline scan
  const baselineScan = kvkScans && kvkScans.length > 0 ? kvkScans.find(scan => scan.isBaseline) : undefined;

  // Use real performance data from API
  const playerGains = useMemo(() => {
    if (!performanceData?.performance_data) return [];

    // Check if we're in baseline-only mode (all deltas are 0)
    const isBaselineOnly = performanceData.performance_data.every((player: any) =>
      (player.kp_delta || 0) === 0 && (player.power_delta || 0) === 0 && (player.dead_delta || 0) === 0
    );

    // Transform API data to match PlayerScanData interface for PlayerTable
    return performanceData.performance_data.map((player: any): PlayerScanData => ({
      governorId: player.governor_id || player.player_id?.toString() || '',
      name: player.player_name || player.name || '',
      alliance: player.alliance || '',
      // Use current values for baseline-only mode, deltas for gains view
      power: isBaselineOnly ? (player.current_power || 0) : (player.power_delta || 0),
      killPoints: isBaselineOnly ? (player.current_kp || 0) : (player.kp_delta || 0),
      deads: isBaselineOnly ? (player.current_dead || 0) : (player.dead_delta || 0),
      t1Kills: 0, // Not available in API response
      t2Kills: 0, // Not available in API response
      t3Kills: 0, // Not available in API response
      t4Kills: isBaselineOnly ? (player.current_t4_kills || 0) : (player.t4_delta || 0),
      t5Kills: isBaselineOnly ? (player.current_t5_kills || 0) : (player.t5_delta || 0),
      totalKills: 0, // Not available in API response
      t45Kills: isBaselineOnly ?
        ((player.current_t4_kills || 0) + (player.current_t5_kills || 0)) :
        (player.t45_delta || (player.t4_delta || 0) + (player.t5_delta || 0)),
      ranged: 0, // Not available in API response
      rssGathered: 0, // Not available in API response
      rssAssisted: 0, // Not available in API response
      helps: 0, // Not available in API response
      // Additional metadata
      statId: player.id,
      scanId: player.scan_id,
      timestamp: player.timestamp
    }));
  }, [performanceData]);

  // Check if we're in baseline-only mode (for UI labels)
  const isBaselineOnly = useMemo(() => {
    return performanceData?.performance_data?.every((player: any) =>
      (player.kp_delta || 0) === 0 && (player.power_delta || 0) === 0 && (player.dead_delta || 0) === 0
    ) || false;
  }, [performanceData]);

  // Use summary stats from API or calculate from player data
  const { totalKillPointsGain, totalT5Casualties, totalDeadsGain, totalPlayers, avgKpPerDead } = useMemo(() => {
    if (performanceData?.summary_stats) {
      // For baseline-only mode, calculate totals from current values
      // For gains mode, use the delta values from summary stats
      const totalKpValue = isBaselineOnly ?
        playerGains.reduce((acc: number, player: PlayerScanData) => acc + (player.killPoints || 0), 0) :
        performanceData.summary_stats.total_kp_gain || 0;

      const totalDeadValue = isBaselineOnly ?
        playerGains.reduce((acc: number, player: PlayerScanData) => acc + (player.deads || 0), 0) :
        performanceData.summary_stats.total_dead_troops || 0;

      return {
        totalKillPointsGain: totalKpValue,
        totalT5Casualties: playerGains.reduce((acc: number, player: PlayerScanData) => acc + (player.t45Kills || 0), 0),
        totalDeadsGain: totalDeadValue,
        totalPlayers: performanceData.summary_stats.total_players || 0,
        avgKpPerDead: totalDeadValue > 0 ? totalKpValue / totalDeadValue : 0
      };
    }

    // Fallback calculation if API data not available
    if (playerGains.length === 0) {
      return {
        totalKillPointsGain: 0,
        totalT5Casualties: 0,
        totalDeadsGain: 0,
        totalPlayers: 0,
        avgKpPerDead: 0
      };
    }

    const stats = playerGains.reduce((acc: any, player: PlayerScanData) => ({
      totalKillPointsGain: acc.totalKillPointsGain + (player.killPoints || 0),
      totalT5Casualties: acc.totalT5Casualties + (player.t45Kills || 0),
      totalDeadsGain: acc.totalDeadsGain + (player.deads || 0),
      totalPlayers: playerGains.length,
      avgKpPerDead: 0 // Will be calculated below
    }), {
      totalKillPointsGain: 0,
      totalT5Casualties: 0,
      totalDeadsGain: 0,
      totalPlayers: 0,
      avgKpPerDead: 0
    });

    // Calculate average KP per dead
    stats.avgKpPerDead = stats.totalDeadsGain > 0 ? stats.totalKillPointsGain / stats.totalDeadsGain : 0;

    return stats;
  }, [playerGains, performanceData, isBaselineOnly]);

  // Sort players by the active tab metric
  const sortedPlayers = useMemo(() => {
    if (!playerGains || playerGains.length === 0) return [];

    if (activeTab === 'overview') {
      return [...playerGains].sort((a, b) => (b.killPoints || 0) - (a.killPoints || 0));
    } else if (activeTab === 'killpoints') {
      return [...playerGains].sort((a, b) => (b.killPoints || 0) - (a.killPoints || 0));
    } else if (activeTab === 'deads') {
      return [...playerGains].sort((a, b) => (b.deads || 0) - (a.deads || 0));
    } else if (activeTab === 't45kills') {
      return [...playerGains].sort((a, b) => (b.t45Kills || 0) - (a.t45Kills || 0));
    }
    return playerGains;
  }, [playerGains, activeTab]);

  // Loading state
  if (isLoadingKvK || isLoadingScans || isLoadingPerformance) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`flex flex-col items-center justify-center min-h-[calc(100vh-200px)] ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
          <p className="text-lg">Loading KvK performance data...</p>
          {performanceData && (
            <p className="text-sm mt-2">Analyzing {performanceData.summary_stats?.total_players || 0} players...</p>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (!kvk) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
          KvK not found. Please check the URL and try again.
        </div>
        <div className="mt-4 text-center">
          <Link
            to="/"
            className={`inline-block px-4 py-2 rounded-md ${theme === 'light'
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-blue-700 text-white hover:bg-blue-600'}`}
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            {/* Back Button */}
            <Link
              to="/kvk-history"
              className={`inline-flex items-center mb-6 px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                theme === 'light'
                  ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-100'
                  : 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/30'
              }`}
            >
              <FaArrowLeft className="mr-2" />
              Back to KvK History
            </Link>

            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex items-center mb-6 lg:mb-0">
                <div className={`p-4 rounded-xl mr-6 ${
                  theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
                }`}>
                  <FaShieldAlt className="text-3xl" />
                </div>
                <div>
                  <h1 className={`text-4xl font-bold ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {kvk.name}
                  </h1>
                  <p className={`text-xl mt-2 ${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}>
                    Season {kvk.season} • Kingdom 2358 Battle Analytics
                  </p>

                  {/* Status and Info */}
                  <div className="flex items-center space-x-6 mt-4">
                    <div className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold ${
                      kvk.status === 'active'
                        ? 'bg-green-500/20 text-green-600'
                        : kvk.status === 'completed'
                        ? 'bg-gray-500/20 text-gray-600'
                        : 'bg-yellow-500/20 text-yellow-600'
                    }`}>
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        kvk.status === 'active' ? 'bg-green-500' : kvk.status === 'completed' ? 'bg-gray-500' : 'bg-yellow-500'
                      }`}></div>
                      {kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}
                    </div>

                    <div className="flex items-center">
                      <FaCalendarAlt className={`text-sm mr-2 ${
                        theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                      }`} />
                      <span className={`text-sm font-medium ${
                        theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                      }`}>
                        Started {formatDate(kvk.startDate)}
                      </span>
                    </div>

                    <div className="flex items-center">
                      <FaEye className={`text-sm mr-2 ${
                        theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                      }`} />
                      <span className={`text-sm font-medium ${
                        theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                      }`}>
                        {sortedScans.length} scan{sortedScans.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Upload Button */}
              {canUploadScans() && kvk.status !== 'completed' && (
                <div className="flex-shrink-0">
                  <Link
                    to={`/upload?kvkId=${kvkId}`}
                    className={`group relative inline-flex items-center px-6 py-4 rounded-xl font-semibold text-white transition-all duration-300 transform hover:scale-105 ${
                      theme === 'light'
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700'
                        : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600'
                    } shadow-lg hover:shadow-xl`}
                  >
                    <FaUpload className="mr-3 text-lg group-hover:animate-bounce" />
                    Upload New Scan
                    <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaShieldAlt className="w-full h-full transform rotate-12" />
          </div>
        </div>

      {/* Summary Cards - Enhanced with Real Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaCrosshairs className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                {isBaselineOnly ? 'Total Kill Points' : 'Total Kill Points Gain'}
              </h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>{formatLargeNumber(totalKillPointsGain)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                {isBaselineOnly ? 'Current baseline values' : `~(${formatLargeNumber(totalKillPointsGain, true)})`}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaShieldAlt className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-purple-500' : 'text-purple-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                {isBaselineOnly ? 'T5 Casualties' : 'T5 Casualties Gain'}
              </h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>{formatLargeNumber(totalT5Casualties)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Severely Wounded/Dead</p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaSkullCrossbones className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                {isBaselineOnly ? 'Total Dead Troops' : 'Deads Gain'}
              </h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'}`}>{formatLargeNumber(totalDeadsGain)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                {isBaselineOnly ? 'Current baseline values' : 'Total dead troops'}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaChartLine className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-green-500' : 'text-green-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Avg KP/Dead</h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>{isNaN(avgKpPerDead) || avgKpPerDead === undefined || avgKpPerDead === null ? '0.00' : avgKpPerDead.toFixed(2)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Efficiency Ratio</p>
            </div>
          </div>
        </div>
      </div>

      {/* Scan Info - Redesigned */}
      <div className={`mb-8 p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        <div className="flex flex-col md:flex-row md:items-start md:justify-between">
          <div>
            <div className="flex items-center mb-2">
              <FaInfoCircle className={`h-5 w-5 mr-2 ${theme === 'light' ? 'text-blue-500' : 'text-blue-400'}`} />
              <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>
                Scan Information
              </h3>
            </div>
            <p className={`mt-1 text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Analysis Period:</span>
              {performanceData?.scan_period ? (
                <span>
                  {performanceData.scan_period.start_date ? formatDate(performanceData.scan_period.start_date) : 'N/A'} → {performanceData.scan_period.end_date ? formatDate(performanceData.scan_period.end_date) : 'N/A'}
                </span>
              ) : (
                <span>{baselineScan ? formatDate(baselineScan.date) : 'N/A'} → {latestScan ? formatDate(latestScan.date) : 'N/A'}</span>
              )}
            </p>
            <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Baseline Scan ID:</span> {performanceData?.baseline_scan || baselineScan?.id || 'N/A'}
            </p>
            <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Latest Scan ID:</span> {performanceData?.latest_scan || latestScan?.id || 'N/A'}
            </p>
          </div>
          <div className="mt-4 md:mt-0 md:text-right">
            <p className={`text-sm flex items-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              <FaCalendarAlt className="h-4 w-4 mr-2 opacity-75" /> Last updated: {latestScan ? timeAgo(latestScan.date) : 'N/A'}
            </p>
            <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              <FaUsers className="h-4 w-4 mr-2 opacity-75" /> Total Players: {totalPlayers || 0}
            </p>
            {performanceData?.summary_stats && (
              <>
                <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  <FaExclamationCircle className="h-4 w-4 mr-2 opacity-75" /> Top Performers: {performanceData.summary_stats.top_performers}
                </p>
                <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  <FaExclamationCircle className="h-4 w-4 mr-2 opacity-75" /> Need Improvement: {performanceData.summary_stats.needs_improvement}
                </p>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Player Performance Dashboard */}
      {sortedPlayers.length > 0 ? (
        <KvKPerformanceDashboard
          players={sortedPlayers}
          summaryStats={performanceData?.summary_stats}
          isBaselineOnly={isBaselineOnly}
        />
      ) : (
        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
          <div className={`text-center py-10 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
            <FaExclamationCircle className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
            <h3 className="text-xl font-semibold mb-3">
              {kvkScans.length === 0 ? "No Scans Available" : "Awaiting Full Data"}
            </h3>
            <p className="text-sm mb-6 max-w-md mx-auto">
              {kvkScans.length === 0
                ? "No scans have been uploaded for this KvK event yet. Upload a scan to begin tracking player statistics."
                : !baselineScan
                ? "A baseline scan is required to calculate player gains. Please upload a new scan and mark it as baseline, or mark an existing scan as baseline."
                : !latestScan
                ? "No recent scan data is available for comparison."
                : "Player statistics will be shown once a baseline scan and at least one subsequent scan are available."}
            </p>
            {canUploadScans() && kvk && kvk.status !== 'completed' && (
              <Link
                to={`/kvk/${kvkId}/upload${!baselineScan && kvkScans.length > 0 ? '?setBaseline=true' : ''}`}
                className={`inline-flex items-center px-6 py-3 rounded-lg text-sm font-medium shadow-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme === 'light'
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'
                  : 'bg-indigo-500 text-white hover:bg-indigo-400 focus:ring-indigo-600'
                }`}
              >
                <FaUpload className="mr-2 h-5 w-5" />
                {kvkScans.length === 0 ? "Upload First Scan" : (!baselineScan ? "Upload/Set Baseline Scan" : "Upload New Scan")}
              </Link>
            )}
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default KvKDetailPage;
