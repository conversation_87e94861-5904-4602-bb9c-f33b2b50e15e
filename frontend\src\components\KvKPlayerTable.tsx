import React, { useState, useMemo, memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import { formatNumber } from '../utils/formatters';
import {
  FaSearch,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaTrophy,
  FaMedal,
  FaAward,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';

interface KvKPlayerTableProps {
  players: PlayerScanData[];
  title?: string;
  pageSize?: number;
  showKPGain?: boolean; // Show KP gain instead of current KP
}

const KvKPlayerTable: React.FC<KvKPlayerTableProps> = ({
  players,
  title = 'Player Rankings',
  pageSize = 25,
  showKPGain = false
}) => {
  const { theme } = useTheme();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof PlayerScanData>('power');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter players based on search term
  const filteredPlayers = useMemo(() => {
    if (!searchTerm) return players;
    
    const searchLower = searchTerm.toLowerCase();
    return players.filter(player =>
      player.name.toLowerCase().includes(searchLower) ||
      player.governorId.toLowerCase().includes(searchLower) ||
      (player.alliance && player.alliance.toLowerCase().includes(searchLower))
    );
  }, [players, searchTerm]);

  // Sort players
  const sortedPlayers = useMemo(() => {
    return [...filteredPlayers].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      return sortDirection === 'asc' 
        ? aStr.localeCompare(bStr)
        : bStr.localeCompare(aStr);
    });
  }, [filteredPlayers, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedPlayers.length / pageSize);
  const paginatedPlayers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedPlayers.slice(startIndex, startIndex + pageSize);
  }, [sortedPlayers, currentPage, pageSize]);

  // Handle sort
  const handleSort = (field: keyof PlayerScanData) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Get rank icon for top 3 players
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <FaTrophy className="text-yellow-500 text-lg" />;
      case 2:
        return <FaMedal className="text-gray-400 text-lg" />;
      case 3:
        return <FaAward className="text-amber-600 text-lg" />;
      default:
        return null;
    }
  };

  // Get sort icon
  const getSortIcon = (field: keyof PlayerScanData) => {
    if (sortField !== field) {
      return <FaSort className="text-gray-400" />;
    }
    return sortDirection === 'asc' 
      ? <FaSortUp className="text-blue-500" />
      : <FaSortDown className="text-blue-500" />;
  };

  return (
    <div className={`rounded-2xl overflow-hidden shadow-xl transition-all duration-300 ${
      theme === 'light' ? 'bg-white' : 'bg-gray-800'
    }`}>
      {/* Header */}
      <div className={`px-6 py-6 ${
        theme === 'light'
          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100'
          : 'bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border-b border-gray-700'
      }`}>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h3 className={`text-xl font-bold ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              {title}
            </h3>
            <p className={`text-sm ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}>
              {filteredPlayers.length} player{filteredPlayers.length !== 1 ? 's' : ''} found
            </p>
          </div>

          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className={`text-sm ${
                theme === 'light' ? 'text-gray-400' : 'text-gray-500'
              }`} />
            </div>
            <input
              type="text"
              placeholder="Search players..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              className={`pl-10 pr-4 py-2 w-64 rounded-xl border transition-all duration-200 ${
                theme === 'light'
                  ? 'bg-white border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                  : 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-800'
              }`}
            />
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className={`${
            theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'
          }`}>
            <tr>
              <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'light' ? 'text-gray-500' : 'text-gray-300'
              }`}>
                Rank
              </th>
              <th 
                className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 transition-colors ${
                  theme === 'light' ? 'text-gray-500 hover:bg-gray-100' : 'text-gray-300 hover:bg-gray-600'
                }`}
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-1">
                  <span>Name</span>
                  {getSortIcon('name')}
                </div>
              </th>
              <th 
                className={`px-6 py-4 text-right text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 transition-colors ${
                  theme === 'light' ? 'text-gray-500 hover:bg-gray-100' : 'text-gray-300 hover:bg-gray-600'
                }`}
                onClick={() => handleSort('power')}
              >
                <div className="flex items-center justify-end space-x-1">
                  <span>Current Power</span>
                  {getSortIcon('power')}
                </div>
              </th>
              <th 
                className={`px-6 py-4 text-right text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 transition-colors ${
                  theme === 'light' ? 'text-gray-500 hover:bg-gray-100' : 'text-gray-300 hover:bg-gray-600'
                }`}
                onClick={() => handleSort('killPoints')}
              >
                <div className="flex items-center justify-end space-x-1">
                  <span>{showKPGain ? 'KP Gain' : 'Kill Points'}</span>
                  {getSortIcon('killPoints')}
                </div>
              </th>
              <th className={`px-6 py-4 text-right text-xs font-medium uppercase tracking-wider ${
                theme === 'light' ? 'text-gray-500' : 'text-gray-300'
              }`}>
                ID
              </th>
            </tr>
          </thead>
          <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-100' : 'divide-gray-700'}`}>
            {paginatedPlayers.map((player, index) => {
              const globalRank = (currentPage - 1) * pageSize + index + 1;
              const isTopThree = globalRank <= 3;
              
              return (
                <tr
                  key={player.governorId}
                  className={`transition-all duration-200 ${
                    isTopThree
                      ? theme === 'light'
                        ? 'bg-gradient-to-r from-yellow-50 to-amber-50 hover:from-yellow-100 hover:to-amber-100'
                        : 'bg-gradient-to-r from-yellow-900/20 to-amber-900/20 hover:from-yellow-900/30 hover:to-amber-900/30'
                      : theme === 'light'
                        ? 'hover:bg-gray-50'
                        : 'hover:bg-gray-700/50'
                  }`}
                >
                  <td className={`px-6 py-4 whitespace-nowrap ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    <div className="flex items-center space-x-2">
                      {getRankIcon(globalRank)}
                      <span className={`font-bold text-lg ${
                        isTopThree 
                          ? theme === 'light' ? 'text-amber-700' : 'text-amber-400'
                          : theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                      }`}>
                        {globalRank}
                      </span>
                    </div>
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    <div className="font-semibold text-base">{player.name}</div>
                    {player.alliance && (
                      <div className={`text-sm ${
                        theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                      }`}>
                        {player.alliance}
                      </div>
                    )}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-right font-semibold text-base ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {formatNumber(player.power)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-right font-semibold text-base ${
                    showKPGain && player.killPoints > 0
                      ? theme === 'light' ? 'text-green-600' : 'text-green-400'
                      : theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {showKPGain && player.killPoints > 0 ? '+' : ''}{formatNumber(player.killPoints)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-right font-mono text-sm ${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {player.governorId}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`px-6 py-4 border-t ${
          theme === 'light' ? 'bg-gray-50 border-gray-200' : 'bg-gray-700 border-gray-600'
        }`}>
          <div className="flex items-center justify-between">
            <div className={`text-sm ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, sortedPlayers.length)} of {sortedPlayers.length} results
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`p-2 rounded-lg transition-colors ${
                  currentPage === 1
                    ? theme === 'light' ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 cursor-not-allowed'
                    : theme === 'light' ? 'text-gray-700 hover:bg-gray-200' : 'text-gray-300 hover:bg-gray-600'
                }`}
              >
                <FaChevronLeft />
              </button>
              <span className={`px-3 py-1 rounded-lg font-medium ${
                theme === 'light' ? 'bg-blue-100 text-blue-800' : 'bg-blue-900 text-blue-200'
              }`}>
                {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`p-2 rounded-lg transition-colors ${
                  currentPage === totalPages
                    ? theme === 'light' ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 cursor-not-allowed'
                    : theme === 'light' ? 'text-gray-700 hover:bg-gray-200' : 'text-gray-300 hover:bg-gray-600'
                }`}
              >
                <FaChevronRight />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(KvKPlayerTable);
