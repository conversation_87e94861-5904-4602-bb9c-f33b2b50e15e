import axios from '../config/axios';
import { KvKData, ScanData, PlayerScanData } from '../types/dataTypes';

// KvK API calls
export const getKvKList = async (): Promise<KvKData[]> => {
  const response = await axios.get('/api/kvks/');
  return response.data.map((kvk: any) => ({
    id: kvk.id.toString(),
    name: kvk.name,
    startDate: kvk.start_date,
    endDate: kvk.end_date,
    status: kvk.status,
    season: kvk.season,
    createdAt: kvk.created_at,
    updatedAt: kvk.updated_at
  }));
};

export const getKvKById = async (id: string): Promise<KvKData | null> => {
  try {
    const response = await axios.get(`/api/kvks/${id}`);
    const kvk = response.data;
    return {
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    };
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

// Scan API calls
export const getScans = async (): Promise<ScanData[]> => {
  const response = await axios.get('/api/scans/');
  return response.data.map((scan: any) => ({
    id: scan.id.toString(),
    name: scan.name,
    date: scan.timestamp,
    isBaseline: scan.is_baseline || false,
    kvkId: scan.kvk_id ? scan.kvk_id.toString() : null,
    kvkPhase: scan.kvk_phase || 'Kingdom vs Kingdom',
    players: [] // Players loaded separately via stats endpoint
  }));
};

export const getScansByKvK = async (kvkId: string): Promise<ScanData[]> => {
  const response = await axios.get(`/api/kvks/${kvkId}/scans`);
  return response.data.map((scan: any) => ({
    id: scan.id.toString(),
    name: scan.name,
    date: scan.timestamp,
    isBaseline: scan.is_baseline || false,
    kvkId: scan.kvk_id ? scan.kvk_id.toString() : null,
    kvkPhase: scan.kvk_phase || 'Kingdom vs Kingdom',
    players: [] // Players loaded separately via stats endpoint
  }));
};

export const getScanById = async (id: string): Promise<ScanData | null> => {
  try {
    const response = await axios.get(`/api/scans/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

export const getScanStats = async (scanId: string): Promise<PlayerScanData[]> => {
  const response = await axios.get(`/api/scans/${scanId}/stats`);
  return response.data;
};

// Player API calls
export const getPlayers = async (): Promise<any[]> => {
  const response = await axios.get('/api/players/');
  return response.data;
};

export const getPlayerById = async (id: string): Promise<any | null> => {
  try {
    const response = await axios.get(`/api/players/${id}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

// Report API calls
export const getFullReport = async (currentScanId?: string, baselineScanId?: string): Promise<any> => {
  const params = new URLSearchParams();
  if (currentScanId) params.append('current_scan_id', currentScanId);
  if (baselineScanId) params.append('baseline_scan_id', baselineScanId);

  const response = await axios.get(`/api/reports/full?${params.toString()}`);
  return response.data;
};

export const getPlayerPerformance = async (playerId: string, currentScanId: string, baselineScanId?: string): Promise<any> => {
  const params = new URLSearchParams();
  params.append('current_scan_id', currentScanId);
  if (baselineScanId) params.append('baseline_scan_id', baselineScanId);

  const response = await axios.get(`/api/reports/player/${playerId}/performance?${params.toString()}`);
  return response.data;
};

export const getKvKPerformanceSummary = async (kvkId: string, limit: number = 50): Promise<any> => {
  const response = await axios.get(`/api/reports/kvk/performance_summary?kvk_id=${kvkId}&limit=${limit}`);
  return response.data;
};

// Dashboard API calls
export const getDashboardData = async (): Promise<any> => {
  const response = await axios.get('/api/dashboard/');
  return response.data;
};

// General Performance API calls (for non-KvK scans)
export const getGeneralPerformanceSummary = async (limit: number = 50): Promise<any> => {
  const response = await axios.get(`/api/reports/general/performance_summary?limit=${limit}`);
  return response.data;
};

// Kingdom Overview APIs
export const getKingdomOverview = async (): Promise<any> => {
  const response = await axios.get('/api/dashboard/kingdom-overview');
  return response.data;
};

export const getGeneralPerformance = async (limit: number = 100): Promise<any> => {
  const response = await axios.get(`/api/dashboard/performance?limit=${limit}`);
  return response.data;
};

// Validation APIs
export const validateScanData = async (scanId: number): Promise<any> => {
  const response = await axios.get(`/api/dashboard/validate-scan/${scanId}`);
  return response.data;
};

export const getPlayerMapping = async (scanId: number): Promise<any> => {
  const response = await axios.get(`/api/dashboard/player-mapping/${scanId}`);
  return response.data;
};

// KvK Summary API
export const getKvKSummary = async (kvkId: string): Promise<any> => {
  const response = await axios.get(`/api/kvks/${kvkId}/summary`);
  return response.data;
};

export const getKvKPerformanceSummary = async (kvkId: string, limit: number = 50): Promise<any> => {
  const response = await axios.get(`/api/kvks/${kvkId}/performance?limit=${limit}`);
  return response.data;
};

// System API calls
export const getParameters = async (): Promise<any[]> => {
  const response = await axios.get('/api/system/parameters');
  return response.data;
};

export const updateParameter = async (name: string, value: number): Promise<any> => {
  const response = await axios.put(`/api/system/parameters/${name}`, { value });
  return response.data;
};