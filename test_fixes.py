#!/usr/bin/env python3
"""
Test script to verify all codebase fixes are working correctly.
Run this from the backend directory: python test_fixes.py
"""

import sys
import os
sys.path.append('.')

from database import SessionLocal
from services import get_general_performance_summary, get_kvk_performance_summary
from routers.dashboard import get_dashboard_data
import crud
import json

def test_general_performance():
    """Test general performance data calculation."""
    print("=" * 60)
    print("TESTING GENERAL PERFORMANCE DATA")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Test general performance summary
        performance_data = get_general_performance_summary(db, limit=10)
        
        print(f"✓ Performance data keys: {list(performance_data.keys())}")
        
        if 'performance_data' in performance_data and performance_data['performance_data']:
            sample_player = performance_data['performance_data'][0]
            print(f"✓ Sample player keys: {list(sample_player.keys())}")
            print(f"✓ Sample player: {sample_player['player_name']}")
            print(f"  - Power delta: {sample_player['power_delta']}")
            print(f"  - KP delta: {sample_player['kp_delta']}")
            print(f"  - T4 delta: {sample_player.get('t4_delta', 'N/A')}")
            print(f"  - T5 delta: {sample_player.get('t5_delta', 'N/A')}")
            print(f"  - T45 delta: {sample_player.get('t45_delta', 'N/A')}")
            
            # Test power losses
            power_losses = [p for p in performance_data['performance_data'] if p['power_delta'] < 0]
            print(f"✓ Players with power losses: {len(power_losses)}")
            
            # Test underperformers
            underperformers = [p for p in performance_data['performance_data'] if p['kp_delta'] == 0]
            print(f"✓ Underperforming players (0 KP change): {len(underperformers)}")
            
            # Test T4-5 calculations
            t45_gains = [p for p in performance_data['performance_data'] if p.get('t45_delta', 0) > 0]
            print(f"✓ Players with T4-5 kills gains: {len(t45_gains)}")
            
        else:
            print("⚠ No performance data found")
            
    except Exception as e:
        print(f"✗ Error in general performance test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def test_dashboard_data():
    """Test dashboard data calculation."""
    print("\n" + "=" * 60)
    print("TESTING DASHBOARD DATA")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        dashboard_data = get_dashboard_data(db)
        
        print(f"✓ Dashboard keys: {list(dashboard_data.keys())}")
        print(f"✓ Total KP: {dashboard_data.get('total_kill_points', 0):,}")
        print(f"✓ KP Gain: {dashboard_data.get('kill_points_gain', 0):,}")
        print(f"✓ Power Gain: {dashboard_data.get('power_gain', 0):,}")
        print(f"✓ Player Count: {dashboard_data.get('player_count', 0)}")
        print(f"✓ T45 Kills Gain: {dashboard_data.get('t45_kills_gain', 'N/A')}")
        
    except Exception as e:
        print(f"✗ Error in dashboard test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def test_kvk_performance():
    """Test KvK performance data calculation."""
    print("\n" + "=" * 60)
    print("TESTING KVK PERFORMANCE DATA")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get first KvK
        kvks = crud.get_kvks(db, limit=1)
        if not kvks:
            print("⚠ No KvKs found, skipping KvK performance test")
            return
            
        kvk = kvks[0]
        print(f"✓ Testing KvK: {kvk.name} (ID: {kvk.id})")
        
        # Test KvK performance summary
        kvk_performance = get_kvk_performance_summary(db, kvk.id, limit=10)
        
        print(f"✓ KvK performance keys: {list(kvk_performance.keys())}")
        
        if 'performance_data' in kvk_performance and kvk_performance['performance_data']:
            sample_player = kvk_performance['performance_data'][0]
            print(f"✓ KvK sample player: {sample_player['player_name']}")
            print(f"  - Power delta: {sample_player['power_delta']}")
            print(f"  - KP delta: {sample_player['kp_delta']}")
            print(f"  - T45 delta: {sample_player.get('t45_delta', 'N/A')}")
            
            # Test KvK power losses
            power_losses = [p for p in kvk_performance['performance_data'] if p['power_delta'] < 0]
            print(f"✓ KvK players with power losses: {len(power_losses)}")
            
        else:
            print("⚠ No KvK performance data found")
            
    except Exception as e:
        print(f"✗ Error in KvK performance test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def test_data_consistency():
    """Test data consistency across different endpoints."""
    print("\n" + "=" * 60)
    print("TESTING DATA CONSISTENCY")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check scans
        baseline_scan = crud.get_baseline_scan(db)
        latest_scan = crud.get_latest_scan(db)
        
        print(f"✓ Baseline scan: {baseline_scan.id if baseline_scan else 'None'}")
        print(f"✓ Latest scan: {latest_scan.id if latest_scan else 'None'}")
        
        if baseline_scan and latest_scan:
            # Check delta stats
            delta_stats = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)
            print(f"✓ Delta stats count: {len(delta_stats) if delta_stats else 0}")
            
            if delta_stats:
                # Check for negative T4-5 deltas (should not exist)
                negative_t45 = 0
                for ds in delta_stats[:10]:  # Check first 10
                    player = ds.player
                    if player:
                        # Get current and baseline stats
                        current_stat = next((stat for stat in latest_scan.player_stats if stat.player_id == ds.player_id), None)
                        baseline_stat = next((stat for stat in baseline_scan.player_stats if stat.player_id == ds.player_id), None)
                        
                        if current_stat and baseline_stat:
                            t4_delta = (current_stat.kill_points_t4 or 0) - (baseline_stat.kill_points_t4 or 0)
                            t5_delta = (current_stat.kill_points_t5 or 0) - (baseline_stat.kill_points_t5 or 0)
                            if t4_delta < 0 or t5_delta < 0:
                                negative_t45 += 1
                
                print(f"✓ Players with negative T4-5 deltas: {negative_t45} (should be 0)")
                
        else:
            print("⚠ Missing baseline or latest scan")
            
    except Exception as e:
        print(f"✗ Error in data consistency test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def main():
    """Run all tests."""
    print("CODEBASE FIXES VERIFICATION")
    print("Testing all major fixes and calculations...")
    
    test_general_performance()
    test_dashboard_data()
    test_kvk_performance()
    test_data_consistency()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETE")
    print("=" * 60)
    print("If all tests show ✓ marks, the fixes are working correctly.")
    print("If you see ✗ marks, there may be remaining issues to address.")
    print("If you see ⚠ marks, the data might be missing but the code is working.")

if __name__ == "__main__":
    main()
