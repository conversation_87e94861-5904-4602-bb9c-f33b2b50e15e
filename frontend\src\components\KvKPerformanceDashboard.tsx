import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import KvKPlayerTable from './KvKPlayerTable';
import {
  <PERSON>aTrophy,
  FaF<PERSON>,
  FaSkullCrossbones,
  FaShieldAlt,
  FaChartLine,
  FaExclamationTriangle,
  FaCrown,
  FaUsers
} from 'react-icons/fa';

interface KvKPerformanceDashboardProps {
  players: PlayerScanData[];
  summaryStats?: {
    total_players: number;
    total_power_gain: number;
    total_power_loss: number;
    net_power_change: number;
    total_kp_gain: number;
    total_dead_troops: number;
    top_performers: number;
    needs_improvement: number;
    power_loss_players: number;
  };
  isBaselineOnly?: boolean;
}

const KvKPerformanceDashboard: React.FC<KvKPerformanceDashboardProps> = ({
  players,
  summaryStats,
  isBaselineOnly = false
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'killpoints' | 'power' | 'deads' | 'powerLoss' | 'underperforming'>('killpoints');

  // Process players based on active tab
  const processedPlayers = useMemo(() => {
    let sortedPlayers = [...players];

    switch (activeTab) {
      case 'killpoints':
        return sortedPlayers.sort((a, b) => (b.killPoints || 0) - (a.killPoints || 0));
      
      case 'power':
        if (isBaselineOnly) {
          return sortedPlayers.sort((a, b) => (b.power || 0) - (a.power || 0));
        } else {
          // Show power gains (positive values)
          return sortedPlayers
            .filter(p => (p.power || 0) > 0)
            .sort((a, b) => (b.power || 0) - (a.power || 0));
        }
      
      case 'powerLoss':
        // Show power losses (negative values, sorted by highest loss)
        return sortedPlayers
          .filter(p => (p.power || 0) < 0)
          .sort((a, b) => (a.power || 0) - (b.power || 0)); // Most negative first
      
      case 'deads':
        return sortedPlayers.sort((a, b) => (b.deads || 0) - (a.deads || 0));
      
      case 'underperforming':
        // Players with zero KP delta (no change in kill points)
        return sortedPlayers
          .filter(p => (p.killPoints || 0) === 0)
          .sort((a, b) => (b.power || 0) - (a.power || 0));
      
      default:
        return sortedPlayers;
    }
  }, [players, activeTab, isBaselineOnly]);

  // Tab configuration
  const tabs = [
    {
      id: 'killpoints' as const,
      label: isBaselineOnly ? 'Kill Points' : 'KP Gains',
      icon: FaTrophy,
      color: 'blue',
      count: isBaselineOnly ? players.length : players.filter(p => (p.killPoints || 0) > 0).length
    },
    {
      id: 'power' as const,
      label: isBaselineOnly ? 'Power' : 'Power Gains',
      icon: FaFire,
      color: 'green',
      count: isBaselineOnly ? players.length : players.filter(p => (p.power || 0) > 0).length
    },
    {
      id: 'powerLoss' as const,
      label: 'Power Losses',
      icon: FaShieldAlt,
      color: 'red',
      count: players.filter(p => (p.power || 0) < 0).length,
      hidden: isBaselineOnly
    },
    {
      id: 'deads' as const,
      label: isBaselineOnly ? 'Dead Troops' : 'Deads Gained',
      icon: FaSkullCrossbones,
      color: 'purple',
      count: isBaselineOnly ? players.length : players.filter(p => (p.deads || 0) > 0).length
    },
    {
      id: 'underperforming' as const,
      label: 'Underperforming',
      icon: FaExclamationTriangle,
      color: 'orange',
      count: players.filter(p => (p.killPoints || 0) === 0).length,
      hidden: isBaselineOnly
    }
  ].filter(tab => !tab.hidden);

  const getTabColorClasses = (color: string, isActive: boolean) => {
    const colors = {
      blue: isActive 
        ? 'bg-blue-500 text-white' 
        : theme === 'light' ? 'text-blue-600 hover:bg-blue-50' : 'text-blue-400 hover:bg-blue-900/20',
      green: isActive 
        ? 'bg-green-500 text-white' 
        : theme === 'light' ? 'text-green-600 hover:bg-green-50' : 'text-green-400 hover:bg-green-900/20',
      red: isActive 
        ? 'bg-red-500 text-white' 
        : theme === 'light' ? 'text-red-600 hover:bg-red-50' : 'text-red-400 hover:bg-red-900/20',
      purple: isActive 
        ? 'bg-purple-500 text-white' 
        : theme === 'light' ? 'text-purple-600 hover:bg-purple-50' : 'text-purple-400 hover:bg-purple-900/20',
      orange: isActive 
        ? 'bg-orange-500 text-white' 
        : theme === 'light' ? 'text-orange-600 hover:bg-orange-50' : 'text-orange-400 hover:bg-orange-900/20'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats Cards */}
      {summaryStats && !isBaselineOnly && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className={`p-4 rounded-xl ${
            theme === 'light' ? 'bg-blue-50 border border-blue-200' : 'bg-blue-900/20 border border-blue-700'
          }`}>
            <div className="flex items-center">
              <FaTrophy className={`text-2xl mr-3 ${
                theme === 'light' ? 'text-blue-600' : 'text-blue-400'
              }`} />
              <div>
                <p className={`text-sm ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
                  Total KP Gained
                </p>
                <p className={`text-xl font-bold ${theme === 'light' ? 'text-blue-800' : 'text-blue-300'}`}>
                  {(summaryStats.total_kp_gain / 1000000).toFixed(1)}M
                </p>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-xl ${
            theme === 'light' ? 'bg-green-50 border border-green-200' : 'bg-green-900/20 border border-green-700'
          }`}>
            <div className="flex items-center">
              <FaFire className={`text-2xl mr-3 ${
                theme === 'light' ? 'text-green-600' : 'text-green-400'
              }`} />
              <div>
                <p className={`text-sm ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                  Net Power Change
                </p>
                <p className={`text-xl font-bold ${
                  summaryStats.net_power_change >= 0
                    ? theme === 'light' ? 'text-green-800' : 'text-green-300'
                    : theme === 'light' ? 'text-red-800' : 'text-red-300'
                }`}>
                  {summaryStats.net_power_change >= 0 ? '+' : ''}{(summaryStats.net_power_change / 1000000).toFixed(1)}M
                </p>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-xl ${
            theme === 'light' ? 'bg-red-50 border border-red-200' : 'bg-red-900/20 border border-red-700'
          }`}>
            <div className="flex items-center">
              <FaShieldAlt className={`text-2xl mr-3 ${
                theme === 'light' ? 'text-red-600' : 'text-red-400'
              }`} />
              <div>
                <p className={`text-sm ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
                  Power Lost
                </p>
                <p className={`text-xl font-bold ${theme === 'light' ? 'text-red-800' : 'text-red-300'}`}>
                  {(summaryStats.total_power_loss / 1000000).toFixed(1)}M
                </p>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-xl ${
            theme === 'light' ? 'bg-orange-50 border border-orange-200' : 'bg-orange-900/20 border border-orange-700'
          }`}>
            <div className="flex items-center">
              <FaExclamationTriangle className={`text-2xl mr-3 ${
                theme === 'light' ? 'text-orange-600' : 'text-orange-400'
              }`} />
              <div>
                <p className={`text-sm ${theme === 'light' ? 'text-orange-600' : 'text-orange-400'}`}>
                  Underperforming
                </p>
                <p className={`text-xl font-bold ${theme === 'light' ? 'text-orange-800' : 'text-orange-300'}`}>
                  {summaryStats.needs_improvement}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Tabs */}
      <div className={`rounded-2xl overflow-hidden shadow-xl ${
        theme === 'light' ? 'bg-white' : 'bg-gray-800'
      }`}>
        {/* Tab Navigation */}
        <div className={`border-b ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
          <div className="flex flex-wrap">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-4 text-sm font-medium transition-colors border-b-2 ${
                    isActive
                      ? 'border-blue-500'
                      : 'border-transparent'
                  } ${getTabColorClasses(tab.color, isActive)}`}
                >
                  <Icon className="mr-2" />
                  <span>{tab.label}</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    isActive
                      ? 'bg-white/20'
                      : theme === 'light' ? 'bg-gray-200 text-gray-700' : 'bg-gray-700 text-gray-300'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Table Content */}
        <div className="p-6">
          <KvKPlayerTable
            players={processedPlayers}
            title={`${tabs.find(t => t.id === activeTab)?.label} Rankings`}
            mode={isBaselineOnly ? 'current' : 'gains'}
            showPowerLoss={activeTab === 'powerLoss'}
            pageSize={50}
          />
        </div>
      </div>
    </div>
  );
};

export default KvKPerformanceDashboard;
